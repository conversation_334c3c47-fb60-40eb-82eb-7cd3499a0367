"""
OpenAI API test module.

This module tests the OpenAI API connection and functionality by sending
a simple chat completion request and displaying the response.
"""
import os
import sys
import openai
from openai import OpenAI

API_KEY = '***************************************************'

client = OpenAI(api_key=API_KEY)

try:
    response = client.chat.completions.create(
        model="gpt-3.5-turbo",  # Veya "gpt-4" kullanabilirsin
        messages=[
            {"role": "user", "content": "Merhab<PERSON>, bu bir testtir."}
        ]
    )

    print("✅ API Anahtarı çalışıyor!")
    print("Modelin yanıtı:")
    print(response.choices[0].message.content)

except openai.AuthenticationError:
    print("❌ API Anahtarı geçersiz veya yetkisiz.")
except openai.RateLimitError:
    print("❌ Rate limit aşıldı. Lütfen daha sonra tekrar deneyin.")
except openai.APIConnectionError:
    print("❌ API bağlantı hatası. İnternet bağlantınızı kontrol edin.")
except openai.APIError as e:
    print(f"❌ OpenAI API hatası: {e}")
except (IndexError, AttributeError, KeyError) as e:
    print(f"⚠️ Yanıt işlenirken hata oluştu: {e}")
except Exception as e:
    print(f"⚠️ Beklenmeyen bir hata oluştu: {e}")
    raise  # Re-raise to help with debugging
